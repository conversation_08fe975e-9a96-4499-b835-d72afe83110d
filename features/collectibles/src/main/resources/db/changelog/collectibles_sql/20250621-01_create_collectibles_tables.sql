-- Drop old tables
DROP TABLE IF EXISTS `collectible`;
DROP TABLE IF EXISTS `collectible_set`;
DROP TABLE IF EXISTS `collectibles_user`;

CREATE TABLE card_collections
(
    id         INT PRIMARY KEY AUTO_INCREMENT,
    name       <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    start_date TIMES<PERSON>MP     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_date   TIMESTAMP NULL,
    status     ENUM('ENABLED', 'EXPIRED', 'DISABLED') NOT NULL DEFAULT 'DISABLED',
    sort_order INT                   DEFAULT 99,
    created_at TIMESTAMP             DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP             DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX      idx_status_dates (status, start_date, end_date),
    UNIQUE KEY uq_collection_name (name)
);

CREATE TABLE cards
(
    id            INT PRIMARY KEY AUTO_INCREMENT,
    collection_id INT          NOT NULL,
    name          <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    start_date    TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_date      TIMESTAMP NULL,
    status        ENUM('ENABLED', 'EXPIRED', 'DISABLED') NOT NULL DEFAULT 'DISABLED',
    image_url     VARCHAR(255) NOT NULL,
    rarity_level  TINYINT      NOT NULL,
    sort_order    TINYINT               DEFAULT 99,
    created_at    TIMESTAMP             DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP             DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (collection_id) REFERENCES card_collections (id) ON DELETE CASCADE,
    INDEX         idx_collection_status_dates (collection_id, status, start_date, end_date),
    UNIQUE KEY uq_cards_collection_name (collection_id, name)
);

CREATE TABLE user_cards_pieces
(
    user_id     BIGINT PRIMARY KEY,
    pieces_data JSON NOT NULL,
    created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

CREATE TABLE rewards
(
    id                   INT PRIMARY KEY AUTO_INCREMENT,
    collection_id        INT  NOT NULL,
    card_id              INT NULL,
    reward_type          ENUM('COMPLETION', 'MILESTONE') NOT NULL,
    milestone_percentage TINYINT NULL,
    reward_data          JSON NOT NULL,
    created_at           TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at           TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    CHECK (
        (reward_type = 'COMPLETION' AND milestone_percentage IS NULL) OR
        (reward_type = 'MILESTONE' AND milestone_percentage IS NOT NULL AND milestone_percentage BETWEEN 1 AND 100)
        ),

    INDEX                idx_milestone_rewards (collection_id, reward_type, milestone_percentage),

    FOREIGN KEY (collection_id) REFERENCES card_collections (id) ON DELETE CASCADE,
    FOREIGN KEY (card_id) REFERENCES cards (id) ON DELETE CASCADE
);

CREATE TABLE user_reward_claims
(
    id                 BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id            BIGINT NOT NULL,
    reward_id          INT    NOT NULL,
    card_collection_id INT    NOT NULL,
    card_id            INT NULL,
    reward_data        JSON   NOT NULL, --copy of reward_data from rewards at the time of claim
    claimed_at         TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at         TIMESTAMP NULL,

    INDEX              idx_user_claims (user_id, claimed_at),
    INDEX              idx_user_reward (user_id, reward_id),
    UNIQUE KEY uk_user_collection_card_reward (user_id, reward_id, card_collection_id, card_id),

    FOREIGN KEY (reward_id) REFERENCES rewards (id),
    FOREIGN KEY (card_collection_id) REFERENCES card_collections (id),
    FOREIGN KEY (card_id) REFERENCES cards (id),
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

CREATE TABLE card_piece_drop_log
(
    id                BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id           BIGINT NOT NULL,
    card_id           INT    NOT NULL,
    card_piece_number INT    NOT NULL,
    drop_source       ENUM('LOOTBOX', 'GAME_PROGRESS', 'EVENT', 'PURCHASE', 'EXCHANGED') NOT NULL,
    context_data      JSON,
    created_at        TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX             idx_user_drops (user_id, created_at),
    INDEX             idx_source_analysis (drop_source, created_at),

    FOREIGN KEY (card_id) REFERENCES cards (id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);